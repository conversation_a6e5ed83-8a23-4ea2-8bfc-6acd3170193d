﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.OperatorManagement;

public class OperatorEntityTypeConfiguration : IEntityTypeConfiguration<Operator>
{
    public void Configure(EntityTypeBuilder<Operator> builder)
    {
        //builder.AddCosmosDbProperties();
        builder.HasIndex(_ => _.OperatorId)
            .IsUnique();
        builder.Property(_ => _.OperatorId)
            .IsRequired();
        builder.Property(_ => _.OrganizationId)
            .IsRequired();
        builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

        //Set up the relationship with org
        builder
            .HasOne(s => s.Organization)
            .WithMany(o => o.Operators)
            .HasForeignKey(s => s.OrganizationId)
            .HasPrincipalKey(r => r.OrganizationId);

        // Configure UnavailableDates as owned entities (value objects)
        builder.OwnsMany(o => o.UnavailableDates, ud =>
        {
            ud.Property(x => x.Date).IsRequired();
            ud.Property(x => x.Reason).IsRequired();
            ud.Property(x => x.Description).HasMaxLength(500);
            ud.HasIndex(x => x.Date); // Index for performance
        });
    }
}