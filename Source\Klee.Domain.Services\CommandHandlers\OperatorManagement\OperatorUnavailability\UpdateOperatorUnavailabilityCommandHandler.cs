using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Commands.OperatorManagement.OperatorUnavailability;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.OperatorUnavailability;

public sealed class UpdateOperatorUnavailabilityCommandHandler
    : RequestHandlerAsync<UpdateOperatorUnavailabilityCommand>
{
    #region PROPERTIES
    private IAppSrpDbContext DbContext { get; }
    private ILogger<UpdateOperatorUnavailabilityCommandHandler> Logger { get; }
    #endregion

    #region CONSTRUCTORS
    public UpdateOperatorUnavailabilityCommandHandler(
        IAppSrpDbContext dbContext,
        ILogger<UpdateOperatorUnavailabilityCommandHandler> logger)
    {
        this.DbContext = dbContext;
        this.Logger = logger;
    }
    #endregion

    #region METHODS
    public override async Task<UpdateOperatorUnavailabilityCommand> HandleAsync(UpdateOperatorUnavailabilityCommand command,
                                                                                CancellationToken cancellationToken = new CancellationToken())
    {
        // Validate dates
        if (command.StartDate > command.EndDate)
        {
            throw new ArgumentException("Start date cannot be after end date.");
        }

        // Find the unavailability period
        var unavailability = await DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability>()
            .FirstOrDefaultAsync(u => u.UnavailabilityId == command.UnavailabilityId && u.IsActive == true, cancellationToken);

        if (unavailability == null)
        {
            throw new ArgumentException($"Unavailability period with ID {command.UnavailabilityId} not found.");
        }

        // Check for overlapping unavailability periods (excluding current one)
        bool hasOverlap = await DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability>()
            .AnyAsync(u => u.OperatorId == unavailability.OperatorId &&
                          u.UnavailabilityId != command.UnavailabilityId &&
                          u.IsActive == true &&
                          u.StartDate <= command.EndDate &&
                          u.EndDate >= command.StartDate, cancellationToken);

        if (hasOverlap)
        {
            throw new InvalidOperationException("The specified period overlaps with an existing unavailability period.");
        }

        // Update properties using reflection to maintain internal setters
        var startDateProperty = typeof(Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability)
            .GetProperty(nameof(unavailability.StartDate));
        var endDateProperty = typeof(Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability)
            .GetProperty(nameof(unavailability.EndDate));
        var reasonProperty = typeof(Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability)
            .GetProperty(nameof(unavailability.Reason));
        var descriptionProperty = typeof(Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability)
            .GetProperty(nameof(unavailability.Description));

        startDateProperty?.SetValue(unavailability, command.StartDate);
        endDateProperty?.SetValue(unavailability, command.EndDate);
        reasonProperty?.SetValue(unavailability, command.Reason);
        descriptionProperty?.SetValue(unavailability, command.Description);

        await DbContext.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Updated unavailability period {UnavailabilityId} for operator {OperatorId}",
            command.UnavailabilityId, unavailability.OperatorId);

        command.Result.Success = true;
        return command;
    }
    #endregion
}
