@using AntDesign
@using Klee.Web.App.Components.UI
@using EnumsNET

@inherits OperatorCalendarAvailabilityViewBase

<Modal Title=@($"Manage Availability - {OperatorDisplayName}")
       @bind-Visible="Visible"
       OnCancel="HandleModalCancel"
       Width="900"
       Footer="null">
    
    @if (ViewModel?.IsLoading == true)
    {
        <div class="text-center py-12">
            <Spin Size="SpinSize.Large" />
            <p class="mt-4 text-gray-600">Loading availability data...</p>
        </div>
    }
    else
    {
        <div class="space-y-6">
            <!-- Calendar Controls -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <h3 class="text-lg font-medium text-teal-700">
                        Manage Operator Availability
                    </h3>
                </div>

                <div class="flex items-center gap-2">
                    <Button Type="@(ViewModel.IsSelectingRange ? ButtonType.Primary : ButtonType.Default)"
                            Class="@(ViewModel.IsSelectingRange ? TailwindStyleStrings.Button.Primary : TailwindStyleStrings.Button.Outline)"
                            OnClick="HandleToggleRangeMode">
                        <i class="fas fa-calendar-week mr-2"></i>
                        Range Mode
                    </Button>

                    <Button Type="@ButtonType.Default"
                            Class="@TailwindStyleStrings.Button.Outline"
                            OnClick="HandleClearSelection">
                        Clear Selection
                    </Button>
                </div>
            </div>

            <!-- Ant Design Calendar -->
            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <Calendar TValue="DateTime?"
                          Value="@ViewModel.CurrentMonth"
                          ValueChanged="@HandleCalendarValueChanged"
                          OnPanelChange="@HandlePanelChange"
                          DateCellRender="@DateCellRender"
                          DateFullCellRender="@DateFullCellRender"
                          Class="operator-availability-calendar" />
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                    <span class="text-gray-600">Available</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                    <span class="text-gray-600">Unavailable</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-teal-100 border border-teal-300 rounded"></div>
                    <span class="text-gray-600">Selected</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-gray-200 border border-gray-400 rounded"></div>
                    <span class="text-gray-600">Past Date</span>
                </div>
            </div>

            @if (ViewModel.SelectedDates.Any())
            {
                <!-- Selection Actions -->
                <Card Class="@TailwindStyleStrings.Card.Container">
                    <div class="p-4">
                        <h4 class="text-md font-medium text-teal-700 mb-3">
                            @ViewModel.SelectedDates.Count date(s) selected
                        </h4>

                        @if (ValidationErrors.Any())
                        {
                            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                                @foreach (var error in ValidationErrors)
                                {
                                    <p class="text-sm text-red-600">@error</p>
                                }
                            </div>
                        }

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Reason</label>
                                <Select TItemValue="string"
                                        TItem="string"
                                        @bind-Value="@ViewModel.ReasonDisplayName"
                                        Placeholder="Select reason"
                                        Class="@TailwindStyleStrings.Form.Select">
                                    @foreach (var reason in OperatorCalendarAvailabilityViewModel.SelectableReasonDisplayNames)
                                    {
                                        <SelectOption TItemValue="string" TItem="string" Value="@reason">@reason</SelectOption>
                                    }
                                </Select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Description (Optional)</label>
                                <Input @bind-Value="@ViewModel.Description"
                                       Placeholder="Additional notes..."
                                       Class="@TailwindStyleStrings.Form.Input" />
                            </div>
                        </div>

                        <div class="flex justify-end gap-2">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="HandleRemoveDates"
                                    Loading="@ViewModel.IsSubmitting">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Mark as Available
                            </Button>
                            
                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    OnClick="HandleAddDates"
                                    Loading="@ViewModel.IsSubmitting">
                                <i class="fas fa-calendar-times mr-2"></i>
                                Mark as Unavailable
                            </Button>
                        </div>
                    </div>
                </Card>
            }
        </div>
    }
</Modal>

@code {
    private void HandleCalendarValueChanged(DateTime? value)
    {
        if (value.HasValue)
        {
            HandleDateClick(value.Value);
        }
    }

    private void HandlePanelChange(DateTime date, string mode)
    {
        ViewModel.CurrentMonth = date;
        StateHasChanged();
    }

    private RenderFragment<DateTime> DateCellRender = (date) => @<div class="@GetDateCellIndicatorClass(date)">
        @if (GetDateIndicatorIcon(date) != null)
        {
            <i class="@GetDateIndicatorIcon(date)"></i>
        }
    </div>;

    private RenderFragment<DateTime> DateFullCellRender = (date) => @<div class="@GetDateFullCellClass(date)"
                                                                          @onclick="@(() => HandleDateClick(date))"
                                                                          style="@GetDateCellStyle(date)">
        <div class="ant-picker-cell-inner ant-picker-calendar-date">
            <div class="ant-picker-calendar-date-value">@date.Day</div>
            <div class="ant-picker-calendar-date-content">
                @if (ViewModel.UnavailableDates.Contains(date))
                {
                    <div class="flex items-center justify-center">
                        <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            Unavailable
                        </span>
                    </div>
                }
                @if (ViewModel.SelectedDates.Contains(date))
                {
                    <div class="flex items-center justify-center mt-1">
                        <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-teal-100 text-teal-800">
                            Selected
                        </span>
                    </div>
                }
            </div>
        </div>
    </div>;

    private static string GetDateCellIndicatorClass(DateTime date)
    {
        return "w-full h-full flex items-center justify-center";
    }

    private string GetDateIndicatorIcon(DateTime date)
    {
        if (ViewModel.SelectedDates.Contains(date))
            return "fas fa-check-circle text-teal-600";
        else if (ViewModel.UnavailableDates.Contains(date))
            return "fas fa-times-circle text-red-600";

        return null;
    }

    private string GetDateFullCellClass(DateTime date)
    {
        var classes = new List<string> { "ant-picker-cell" };

        if (date < DateTime.Today)
        {
            classes.Add("ant-picker-cell-disabled");
            classes.Add("cursor-not-allowed");
        }
        else
        {
            classes.Add("cursor-pointer");
            classes.Add("hover:bg-teal-50");
        }

        if (ViewModel.SelectedDates.Contains(date))
        {
            classes.Add("ant-picker-cell-selected");
            classes.Add("bg-teal-100");
            classes.Add("border-teal-300");
        }
        else if (ViewModel.UnavailableDates.Contains(date))
        {
            classes.Add("bg-red-50");
            classes.Add("border-red-200");
        }

        return string.Join(" ", classes);
    }

    private string GetDateCellStyle(DateTime date)
    {
        if (date < DateTime.Today)
            return "cursor: not-allowed; opacity: 0.5;";
        else
            return "cursor: pointer;";
    }
}
