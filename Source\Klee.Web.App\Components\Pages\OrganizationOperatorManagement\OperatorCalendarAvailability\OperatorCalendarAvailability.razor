@using AntDesign
@using Klee.Web.App.Components.UI
@using EnumsNET

@inherits OperatorCalendarAvailabilityViewBase

<style>
    .operator-availability-calendar .ant-picker-calendar-date-content {
        min-height: 24px;
    }

    .operator-availability-calendar .ant-picker-cell:hover {
        background-color: #f0fdfa !important;
    }

    .operator-availability-calendar .ant-picker-cell-selected {
        background-color: #f0fdfa !important;
        border-color: #0f766e !important;
    }

    .operator-availability-calendar .ant-picker-cell-today .ant-picker-calendar-date-value {
        color: #0f766e;
        font-weight: 600;
    }

    .operator-availability-calendar .ant-picker-calendar-date-value {
        font-size: 14px;
        line-height: 22px;
    }

    .hover\\:bg-teal-25:hover {
        background-color: #f0fdfa;
    }
</style>

<Modal Title=@($"Manage Availability - {OperatorDisplayName}")
       @bind-Visible="Visible"
       OnCancel="HandleModalCancel"
       Width="900"
       Footer="null">
    
    @if (ViewModel?.IsLoading == true)
    {
        <div class="text-center py-12">
            <Spin Size="SpinSize.Large" />
            <p class="mt-4 text-gray-600">Loading availability data...</p>
        </div>
    }
    else
    {
        <div class="space-y-6">
            <!-- Date Range Selection -->
            <Card Class="@TailwindStyleStrings.Card.Container">
                <div class="p-4">
                    <h4 class="text-md font-medium text-teal-700 mb-4">Select Unavailable Dates</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-teal-700 mb-2">Select Date Range</label>
                        <RangePicker TValue="DateTime[]"
                                     Value="@SelectedDateRange"
                                     ValueChanged="@HandleDateRangeChanged"
                                     Format="dd/MM/yyyy"
                                     Placeholder="@(new[] { "Start date", "End date" })"
                                     DisabledDate="@IsDateDisabled"
                                     Class="w-full" />
                    </div>

                    @if (ViewModel?.SelectedDates?.Any() == true)
                    {
                        <div class="mb-4 p-3 bg-teal-50 border border-teal-200 rounded-md">
                            <p class="text-sm text-teal-700 font-medium mb-2">Selected Dates (@ViewModel.SelectedDates.Count):</p>
                            <div class="flex flex-wrap gap-2">
                                @foreach (var date in ViewModel.SelectedDates.OrderBy(d => d))
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                        @date.ToString("MMM dd, yyyy")
                                        <button type="button"
                                                class="ml-1 text-teal-600 hover:text-teal-800"
                                                @onclick="@(() => RemoveSingleDate(date))">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                    </span>
                                }
                            </div>
                            <Button Type="@ButtonType.Default"
                                    Class="@($"{TailwindStyleStrings.Button.Outline} mt-2")"
                                    Size="@ButtonSize.Small"
                                    OnClick="HandleClearSelection">
                                Clear All
                            </Button>
                        </div>

                        <!-- Reason and Description -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Reason</label>
                                <Select TItemValue="string"
                                        TItem="string"
                                        @bind-Value="@ViewModel.ReasonDisplayName"
                                        Placeholder="Select reason"
                                        Class="w-full">
                                    @foreach (var reason in OperatorCalendarAvailabilityViewModel.SelectableReasonDisplayNames)
                                    {
                                        <SelectOption TItemValue="string" TItem="string" Value="@reason">@reason</SelectOption>
                                    }
                                </Select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Description (Optional)</label>
                                <Input @bind-Value="@ViewModel.Description"
                                       Placeholder="Additional notes..."
                                       Class="w-full" />
                            </div>
                        </div>

                        <div class="flex justify-end gap-2">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="HandleRemoveDates"
                                    Loading="@(ViewModel?.IsSubmitting == true)">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Mark as Available
                            </Button>

                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    OnClick="HandleAddDates"
                                    Loading="@(ViewModel?.IsSubmitting == true)">
                                <i class="fas fa-calendar-times mr-2"></i>
                                Mark as Unavailable
                            </Button>
                        </div>
                    }
                </div>
            </Card>

            <!-- Calendar Display -->
            <Card Class="@TailwindStyleStrings.Card.Container">
                <div class="p-4">
                    <h4 class="text-md font-medium text-teal-700 mb-4">Availability Overview</h4>

                    <div class="operator-availability-calendar">
                        <Calendar TValue="DateTime?"
                                  DateFullCellRender="@GetDateFullCellRender"
                                  Class="w-full" />
                    </div>

                    <!-- Legend -->
                    <div class="flex items-center justify-center gap-6 text-sm mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                            <span class="text-gray-600">Available</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                            <span class="text-gray-600">Unavailable</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-teal-100 border border-teal-300 rounded"></div>
                            <span class="text-gray-600">Selected for Action</span>
                        </div>
                    </div>
                </div>
            </Card>



            @if (ValidationErrors?.Any() == true)
            {
                <Card Class="@TailwindStyleStrings.Card.Container">
                    <div class="p-4">
                        <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            @foreach (var error in ValidationErrors)
                            {
                                <p class="text-sm text-red-600">@error</p>
                            }
                        </div>
                    </div>
                </Card>
            }
        </div>
    }
</Modal>

@code {
    private DateTime[] SelectedDateRange { get; set; } = Array.Empty<DateTime>();

    private void HandleDateRangeChanged(DateTime[] dateRange)
    {
        if (dateRange != null && dateRange.Length == 2 && ViewModel != null)
        {
            SelectedDateRange = dateRange;

            // Clear existing selection and add range
            ViewModel.ClearSelection();

            var startDate = dateRange[0];
            var endDate = dateRange[1];

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                if (date >= DateTime.Today)
                {
                    ViewModel.SelectedDates.Add(date);
                }
            }

            StateHasChanged();
        }
    }

    private void RemoveSingleDate(DateTime date)
    {
        if (ViewModel?.SelectedDates != null)
        {
            ViewModel.SelectedDates.Remove(date);
            StateHasChanged();
        }
    }

    private bool IsDateDisabled(DateTime date)
    {
        return date < DateTime.Today;
    }

    private Func<DateTime, RenderFragment> GetDateFullCellRender => (date) => @<div class="@GetDateFullCellClass(date)">
        <div class="ant-picker-cell-inner ant-picker-calendar-date">
            <div class="ant-picker-calendar-date-value">@date.Day</div>
            <div class="ant-picker-calendar-date-content">
                @if (ViewModel?.UnavailableDates?.Contains(date) == true)
                {
                    @if (ViewModel?.UnavailableDateDetails?.ContainsKey(date) == true)
                    {
                        var details = ViewModel.UnavailableDateDetails[date];
                        <div class="flex items-center justify-center">
                            <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800"
                                  title="@($"{details.Reason}: {details.Description}")">
                                <i class="fas fa-times mr-1"></i>
                                @details.Reason.ToString()
                            </span>
                        </div>
                    }
                    else
                    {
                        <div class="flex items-center justify-center">
                            <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-times mr-1"></i>
                                Unavailable
                            </span>
                        </div>
                    }
                }
                @if (ViewModel?.SelectedDates?.Contains(date) == true)
                {
                    <div class="flex items-center justify-center mt-1">
                        <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-teal-100 text-teal-800">
                            <i class="fas fa-check mr-1"></i>
                            Selected
                        </span>
                    </div>
                }
            </div>
        </div>
    </div>;

    private string GetDateFullCellClass(DateTime date)
    {
        var classes = new List<string> { "ant-picker-cell" };

        if (date < DateTime.Today)
        {
            classes.Add("ant-picker-cell-disabled");
            classes.Add("cursor-not-allowed");
            classes.Add("opacity-50");
        }

        if (ViewModel?.SelectedDates?.Contains(date) == true)
        {
            classes.Add("ant-picker-cell-selected");
            classes.Add("bg-teal-50");
            classes.Add("border-teal-200");
        }
        else if (ViewModel?.UnavailableDates?.Contains(date) == true)
        {
            classes.Add("bg-red-50");
            classes.Add("border-red-200");
        }
        else if (date >= DateTime.Today)
        {
            classes.Add("hover:bg-teal-50");
        }

        return string.Join(" ", classes);
    }
}
