@using AntDesign
@using Klee.Web.App.Components.UI
@using EnumsNET

@inherits OperatorCalendarAvailabilityViewBase

<style>
    .operator-availability-calendar .ant-picker-calendar-date-content {
        min-height: 24px;
    }

    .operator-availability-calendar .ant-picker-cell:hover {
        background-color: #f0fdfa !important;
    }

    .operator-availability-calendar .ant-picker-cell-selected {
        background-color: #f0fdfa !important;
        border-color: #0f766e !important;
    }

    .operator-availability-calendar .ant-picker-cell-today .ant-picker-calendar-date-value {
        color: #0f766e;
        font-weight: 600;
    }

    .operator-availability-calendar .ant-picker-calendar-date-value {
        font-size: 14px;
        line-height: 22px;
    }

    .hover\\:bg-teal-25:hover {
        background-color: #f0fdfa;
    }
</style>

<Modal Title=@($"Manage Availability - {OperatorDisplayName}")
       @bind-Visible="Visible"
       OnCancel="HandleModalCancel"
       Width="900"
       Footer="null">
    
    @if (ViewModel?.IsLoading == true)
    {
        <div class="text-center py-12">
            <Spin Size="SpinSize.Large" />
            <p class="mt-4 text-gray-600">Loading availability data...</p>
        </div>
    }
    else
    {
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-teal-700">
                    Manage Operator Availability
                </h3>
            </div>

            <!-- Date Selection Section -->
            <Card Class="@TailwindStyleStrings.Card.Container">
                <div class="p-4">
                    <h4 class="text-md font-medium text-teal-700 mb-4">Select Dates to Manage</h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-teal-700 mb-2">Single Date Selection</label>
                            <DatePicker TValue="DateTime?"
                                        Value="@SelectedSingleDate"
                                        ValueChanged="@HandleSingleDateChanged"
                                        Format="dd/MM/yyyy"
                                        Placeholder=@("Select a date")
                                        DisabledDate="@IsDateDisabled"
                                        Class=@($"{TailwindStyleStrings.Form.Input} w-full") />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-teal-700 mb-2">Date Range Selection</label>
                            <RangePicker TValue="DateTime[]"
                                         Value="@SelectedDateRange"
                                         ValueChanged="@HandleDateRangeChanged"
                                         Format="dd/MM/yyyy"
                                         Placeholder="@(new[] { "Start date", "End date" })"
                                         DisabledDate="@IsDateDisabled"
                                         Class=@($"{TailwindStyleStrings.Form.Input} w-full") />
                        </div>
                    </div>

                    @if (ViewModel.SelectedDates.Any())
                    {
                        <div class="mb-4 p-3 bg-teal-50 border border-teal-200 rounded-md">
                            <p class="text-sm text-teal-700 font-medium mb-2">Selected Dates (@ViewModel.SelectedDates.Count):</p>
                            <div class="flex flex-wrap gap-2">
                                @foreach (var date in ViewModel.SelectedDates.OrderBy(d => d))
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                        @date.ToString("MMM dd, yyyy")
                                        <button type="button"
                                                class="ml-1 text-teal-600 hover:text-teal-800"
                                                @onclick="@(() => RemoveSingleDate(date))">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                    </span>
                                }
                            </div>
                            <Button Type="@ButtonType.Default"
                                    Class="@($"{TailwindStyleStrings.Button.Outline} mt-2")"
                                    Size="@ButtonSize.Small"
                                    OnClick="HandleClearSelection">
                                Clear All
                            </Button>
                        </div>
                    }
                </div>
            </Card>

            <!-- Calendar Display -->
            <Card Class="@TailwindStyleStrings.Card.Container">
                <div class="p-4">
                    <h4 class="text-md font-medium text-teal-700 mb-4">Availability Overview</h4>

                    <div class="operator-availability-calendar">
                        <Calendar TValue="DateTime?"
                                  DateCellRender="@GetDateCellRender()"
                                  DateFullCellRender="@GetDateFullCellRender()"
                                  OnSelect="@HandleCalendarDateSelect"
                                  Class="w-full" />
                    </div>

                    <!-- Legend -->
                    <div class="flex items-center justify-center gap-6 text-sm mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                            <span class="text-gray-600">Available</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                            <span class="text-gray-600">Unavailable</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-teal-100 border border-teal-300 rounded"></div>
                            <span class="text-gray-600">Selected</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-gray-200 border border-gray-400 rounded"></div>
                            <span class="text-gray-600">Past Date</span>
                        </div>
                    </div>
                </div>
            </Card>



            @if (ViewModel.SelectedDates.Any())
            {
                <!-- Selection Actions -->
                <Card Class="@TailwindStyleStrings.Card.Container">
                    <div class="p-4">
                        <h4 class="text-md font-medium text-teal-700 mb-3">
                            @ViewModel.SelectedDates.Count date(s) selected
                        </h4>

                        @if (ValidationErrors.Any())
                        {
                            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                                @foreach (var error in ValidationErrors)
                                {
                                    <p class="text-sm text-red-600">@error</p>
                                }
                            </div>
                        }

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Reason</label>
                                <Select TItemValue="string"
                                        TItem="string"
                                        @bind-Value="@ViewModel.ReasonDisplayName"
                                        Placeholder="Select reason"
                                        Class="@TailwindStyleStrings.Form.Select">
                                    @foreach (var reason in OperatorCalendarAvailabilityViewModel.SelectableReasonDisplayNames)
                                    {
                                        <SelectOption TItemValue="string" TItem="string" Value="@reason">@reason</SelectOption>
                                    }
                                </Select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Description (Optional)</label>
                                <Input @bind-Value="@ViewModel.Description"
                                       Placeholder="Additional notes..."
                                       Class="@TailwindStyleStrings.Form.Input" />
                            </div>
                        </div>

                        <div class="flex justify-end gap-2">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="HandleRemoveDates"
                                    Loading="@ViewModel.IsSubmitting">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Mark as Available
                            </Button>
                            
                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    OnClick="HandleAddDates"
                                    Loading="@ViewModel.IsSubmitting">
                                <i class="fas fa-calendar-times mr-2"></i>
                                Mark as Unavailable
                            </Button>
                        </div>
                    </div>
                </Card>
            }
        </div>
    }
</Modal>

@code {
    private DateTime? SelectedSingleDate { get; set; }
    private DateTime[] SelectedDateRange { get; set; } = new DateTime[0];

    private void HandleSingleDateChanged(DateTime? date)
    {
        if (date.HasValue && date.Value >= DateTime.Today)
        {
            SelectedSingleDate = date;
            ViewModel.ToggleDateSelection(date.Value);
            StateHasChanged();
        }
    }

    private void HandleDateRangeChanged(DateTime[] dateRange)
    {
        if (dateRange != null && dateRange.Length == 2)
        {
            SelectedDateRange = dateRange;

            // Clear existing selection and add range
            ViewModel.ClearSelection();

            var startDate = dateRange[0];
            var endDate = dateRange[1];

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                if (date >= DateTime.Today)
                {
                    ViewModel.SelectedDates.Add(date);
                }
            }

            StateHasChanged();
        }
    }

    private void HandleCalendarDateSelect(DateTime date)
    {
        if (date >= DateTime.Today)
        {
            ViewModel.ToggleDateSelection(date);
            StateHasChanged();
        }
    }

    private void RemoveSingleDate(DateTime date)
    {
        ViewModel.SelectedDates.Remove(date);
        StateHasChanged();
    }

    private bool IsDateDisabled(DateTime date)
    {
        return date < DateTime.Today;
    }

    private Func<DateTime, RenderFragment> GetDateCellRender()
    {
        return (date) => @<div class="w-full h-full flex items-center justify-center">
            @if (GetDateIndicatorIcon(date) != null)
            {
                <i class="@GetDateIndicatorIcon(date)"></i>
            }
        </div>;
    }

    private Func<DateTime, RenderFragment> GetDateFullCellRender()
    {
        return (date) => @<div class="@GetDateFullCellClass(date)"
                               style="@GetDateCellStyle(date)">
            <div class="ant-picker-cell-inner ant-picker-calendar-date">
                <div class="ant-picker-calendar-date-value">@date.Day</div>
                <div class="ant-picker-calendar-date-content">
                    @if (ViewModel?.UnavailableDates?.Contains(date) == true)
                    {
                        <div class="flex items-center justify-center">
                            <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-times mr-1"></i>
                                Unavailable
                            </span>
                        </div>
                    }
                    @if (ViewModel?.SelectedDates?.Contains(date) == true)
                    {
                        <div class="flex items-center justify-center mt-1">
                            <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-teal-100 text-teal-800">
                                <i class="fas fa-check mr-1"></i>
                                Selected
                            </span>
                        </div>
                    }
                    @if (ViewModel?.UnavailableDateDetails?.ContainsKey(date) == true)
                    {
                        var details = ViewModel.UnavailableDateDetails[date];
                        <div class="flex items-center justify-center mt-1">
                            <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700"
                                  title="@details.Description">
                                @details.Reason.ToString()
                            </span>
                        </div>
                    }
                </div>
            </div>
        </div>;
    }

    private string GetDateIndicatorIcon(DateTime date)
    {
        if (ViewModel?.SelectedDates?.Contains(date) == true)
            return "fas fa-check-circle text-teal-600 text-xs";
        else if (ViewModel?.UnavailableDates?.Contains(date) == true)
            return "fas fa-times-circle text-red-600 text-xs";

        return null;
    }

    private string GetDateFullCellClass(DateTime date)
    {
        var classes = new List<string> { "ant-picker-cell" };

        if (date < DateTime.Today)
        {
            classes.Add("ant-picker-cell-disabled");
            classes.Add("cursor-not-allowed");
            classes.Add("opacity-50");
        }
        else
        {
            classes.Add("cursor-pointer");
        }

        if (ViewModel?.SelectedDates?.Contains(date) == true)
        {
            classes.Add("ant-picker-cell-selected");
            classes.Add("bg-teal-50");
            classes.Add("border-teal-200");
        }
        else if (ViewModel?.UnavailableDates?.Contains(date) == true)
        {
            classes.Add("bg-red-50");
            classes.Add("border-red-200");
        }
        else if (date >= DateTime.Today)
        {
            classes.Add("hover:bg-teal-25");
        }

        return string.Join(" ", classes);
    }

    private string GetDateCellStyle(DateTime date)
    {
        if (date < DateTime.Today)
            return "cursor: not-allowed;";
        else
            return "cursor: pointer;";
    }
}
