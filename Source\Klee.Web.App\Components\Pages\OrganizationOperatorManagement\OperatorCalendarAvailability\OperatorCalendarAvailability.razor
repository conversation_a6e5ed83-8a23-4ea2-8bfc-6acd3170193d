@using AntDesign
@using Klee.Web.App.Components.UI
@using EnumsNET

@inherits OperatorCalendarAvailabilityViewBase

<Modal Title=@($"Manage Availability - {OperatorDisplayName}")
       @bind-Visible="Visible"
       OnCancel="HandleModalCancel"
       Width="900"
       Footer="null">
    
    @if (ViewModel?.IsLoading == true)
    {
        <div class="text-center py-12">
            <Spin Size="SpinSize.Large" />
            <p class="mt-4 text-gray-600">Loading availability data...</p>
        </div>
    }
    else
    {
        <div class="space-y-6">
            <!-- Calendar Controls -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <Button Type="@ButtonType.Default"
                            Class="@TailwindStyleStrings.Button.Outline"
                            OnClick="HandlePreviousMonth">
                        <i class="fas fa-chevron-left"></i>
                    </Button>
                    
                    <h3 class="text-lg font-medium text-teal-700">
                        @ViewModel.CurrentMonth.ToString("MMMM yyyy")
                    </h3>
                    
                    <Button Type="@ButtonType.Default"
                            Class="@TailwindStyleStrings.Button.Outline"
                            OnClick="HandleNextMonth">
                        <i class="fas fa-chevron-right"></i>
                    </Button>
                </div>

                <div class="flex items-center gap-2">
                    <Button Type="@(ViewModel.IsSelectingRange ? ButtonType.Primary : ButtonType.Default)"
                            Class="@(ViewModel.IsSelectingRange ? TailwindStyleStrings.Button.Primary : TailwindStyleStrings.Button.Outline)"
                            OnClick="HandleToggleRangeMode">
                        <i class="fas fa-calendar-week mr-2"></i>
                        Range Mode
                    </Button>
                    
                    <Button Type="@ButtonType.Default"
                            Class="@TailwindStyleStrings.Button.Outline"
                            OnClick="HandleClearSelection">
                        Clear Selection
                    </Button>
                </div>
            </div>

            <!-- Calendar Grid -->
            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <!-- Day Headers -->
                <div class="grid grid-cols-7 bg-gray-50 border-b border-gray-200">
                    @foreach (var dayName in new[] { "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun" })
                    {
                        <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200 last:border-r-0">
                            @dayName
                        </div>
                    }
                </div>

                <!-- Calendar Days -->
                <div class="grid grid-cols-7">
                    @foreach (var date in GetCalendarDays())
                    {
                        var isCurrentMonth = IsCurrentMonth(date);
                        var isSelectable = ViewModel.IsDateSelectable(date);
                        var statusClass = ViewModel.GetDateStatusClass(date);
                        var isSelected = ViewModel.SelectedDates.Contains(date);
                        var isUnavailable = ViewModel.UnavailableDates.Contains(date);
                        
                        <div class="@($"relative h-12 border-r border-b border-gray-200 last:border-r-0 {GetDateCellClass(date, isCurrentMonth, isSelectable, isSelected, isUnavailable)}")"
                             @onclick="@(() => HandleDateClick(date))"
                             style="@(isSelectable ? "cursor: pointer;" : "cursor: not-allowed;")">
                            
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="@($"text-sm {GetDateTextClass(date, isCurrentMonth, isSelectable, isSelected, isUnavailable)}")">
                                    @date.Day
                                </span>
                            </div>

                            @if (isUnavailable && ViewModel.UnavailableDateDetails.ContainsKey(date))
                            {
                                <div class="absolute top-1 right-1">
                                    <i class="fas fa-circle text-xs text-red-500"></i>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-center gap-6 text-sm">
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                    <span class="text-gray-600">Available</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                    <span class="text-gray-600">Unavailable</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-teal-100 border border-teal-300 rounded"></div>
                    <span class="text-gray-600">Selected</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-gray-200 border border-gray-400 rounded"></div>
                    <span class="text-gray-600">Past Date</span>
                </div>
            </div>

            @if (ViewModel.SelectedDates.Any())
            {
                <!-- Selection Actions -->
                <Card Class="@TailwindStyleStrings.Card.Container">
                    <div class="p-4">
                        <h4 class="text-md font-medium text-teal-700 mb-3">
                            @ViewModel.SelectedDates.Count date(s) selected
                        </h4>

                        @if (ValidationErrors.Any())
                        {
                            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                                @foreach (var error in ValidationErrors)
                                {
                                    <p class="text-sm text-red-600">@error</p>
                                }
                            </div>
                        }

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Reason</label>
                                <Select TItemValue="string"
                                        TItem="string"
                                        @bind-Value="@ViewModel.ReasonDisplayName"
                                        Placeholder="Select reason"
                                        Class="@TailwindStyleStrings.Form.Select">
                                    @foreach (var reason in OperatorCalendarAvailabilityViewModel.SelectableReasonDisplayNames)
                                    {
                                        <SelectOption TItemValue="string" TItem="string" Value="@reason">@reason</SelectOption>
                                    }
                                </Select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-teal-700 mb-1">Description (Optional)</label>
                                <Input @bind-Value="@ViewModel.Description"
                                       Placeholder="Additional notes..."
                                       Class="@TailwindStyleStrings.Form.Input" />
                            </div>
                        </div>

                        <div class="flex justify-end gap-2">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="HandleRemoveDates"
                                    Loading="@ViewModel.IsSubmitting">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Mark as Available
                            </Button>
                            
                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    OnClick="HandleAddDates"
                                    Loading="@ViewModel.IsSubmitting">
                                <i class="fas fa-calendar-times mr-2"></i>
                                Mark as Unavailable
                            </Button>
                        </div>
                    </div>
                </Card>
            }
        </div>
    }
</Modal>

@code {
    private string GetDateCellClass(DateTime date, bool isCurrentMonth, bool isSelectable, bool isSelected, bool isUnavailable)
    {
        var classes = new List<string>();
        
        if (!isCurrentMonth)
            classes.Add("bg-gray-50");
        else if (!isSelectable)
            classes.Add("bg-gray-200");
        else if (isSelected)
            classes.Add("bg-teal-100 border-teal-300");
        else if (isUnavailable)
            classes.Add("bg-red-100 border-red-300");
        else
            classes.Add("bg-white hover:bg-gray-50");
            
        if (isSelectable)
            classes.Add("hover:bg-teal-50");
            
        return string.Join(" ", classes);
    }
    
    private string GetDateTextClass(DateTime date, bool isCurrentMonth, bool isSelectable, bool isSelected, bool isUnavailable)
    {
        if (!isCurrentMonth)
            return "text-gray-400";
        else if (!isSelectable)
            return "text-gray-500";
        else if (isSelected)
            return "text-teal-700 font-medium";
        else if (isUnavailable)
            return "text-red-700 font-medium";
        else
            return "text-gray-900";
    }
}
