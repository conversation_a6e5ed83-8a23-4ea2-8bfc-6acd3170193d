using System;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OperatorManagement.OperatorUnavailability;

public class UpdateOperatorUnavailabilityCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public bool Success { get; set; } = false;
    }
    #endregion

    #region PROPERTIES
    public string UnavailabilityId { get; set; } = "";
    public DateTime StartDate { get; set; } = DateTime.Today;
    public DateTime EndDate { get; set; } = DateTime.Today;
    public UnavailabilityReasonIds Reason { get; set; } = UnavailabilityReasonIds.Holiday;
    public string Description { get; set; } = "";

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateOperatorUnavailabilityCommand(ICommandContext context)
        : base(context)
    {
    }
    #endregion
}
