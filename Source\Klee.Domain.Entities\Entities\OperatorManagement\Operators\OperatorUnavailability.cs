using System;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Renoir.Application.Domain;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Entities.OperatorManagement.Operators;

public class OperatorUnavailability
    : DomainEntityAggregateRootBase<long>
{
    #region PROPERTIES - IDENTIFICATION
    /// <summary>
    /// The Seafar internal id of the unavailability period
    /// </summary>
    [Required]
    public string UnavailabilityId { get; internal set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Start date of the unavailability period
    /// </summary>
    [Required]
    public DateTime StartDate { get; internal set; } = DateTime.UtcNow.Date;

    /// <summary>
    /// End date of the unavailability period
    /// </summary>
    [Required]
    public DateTime EndDate { get; internal set; } = DateTime.UtcNow.Date;

    /// <summary>
    /// Reason for unavailability
    /// </summary>
    [Required]
    public UnavailabilityReasonIds Reason { get; internal set; } = UnavailabilityReasonIds.Holiday;

    /// <summary>
    /// Optional description/notes for the unavailability period
    /// </summary>
    public string Description { get; internal set; } = "";
    #endregion

    #region PROPERTIES - SYSTEM
    /// <summary>
    /// The software environment on which the OperatorUnavailability is used
    /// </summary>
    public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;

    /// <summary>
    /// Is active when the OperatorUnavailability is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;
    #endregion

    #region PROPERTIES - RELATIONS
    /// <summary>
    /// ID of the operator this unavailability period belongs to
    /// </summary>
    [Required]
    public string OperatorId { get; internal set; } = "";

    public Operator Operator { get; internal set; }
    #endregion

    #region CONSTRUCTORS
    public OperatorUnavailability()
    {
    }

    public OperatorUnavailability(string operatorId, DateTime startDate, DateTime endDate, UnavailabilityReasonIds reason, string description = "")
    {
        this.OperatorId = operatorId;
        this.StartDate = startDate.Date;
        this.EndDate = endDate.Date;
        this.Reason = reason;
        this.Description = description;
    }
    #endregion

    #region METHODS - ENTITY
    public override string CreateEntityPartitionKey()
    {
        return this.UnavailabilityId;
    }

    public override string GetEntityId2()
    {
        return this.UnavailabilityId;
    }

    public override string GetEntityTypeName()
    {
        return "OperatorUnavailability";
    }
    #endregion

    #region METHODS - VALIDATION
    /// <summary>
    /// Validates that the unavailability period is valid
    /// </summary>
    public bool IsValidPeriod()
    {
        return StartDate <= EndDate;
    }

    /// <summary>
    /// Checks if this unavailability period overlaps with a given date range
    /// </summary>
    public bool OverlapsWith(DateTime startDateTime, DateTime endDateTime)
    {
        DateTime startDateOnly = startDateTime.Date;
        DateTime endDateOnly = endDateTime.Date;
        
        return StartDate <= endDateOnly && EndDate >= startDateOnly;
    }
    #endregion
}
