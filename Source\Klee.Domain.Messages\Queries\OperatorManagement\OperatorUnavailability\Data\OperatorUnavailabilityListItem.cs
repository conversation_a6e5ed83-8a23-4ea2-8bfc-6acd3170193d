using System;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;

namespace Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability.Data;

public class OperatorUnavailabilityListItem
{
    #region PROPERTIES
    public long EntityId { get; set; }
    public string UnavailabilityId { get; set; } = "";
    public string OperatorId { get; set; } = "";
    public DateTime StartDate { get; set; } = DateTime.Today;
    public DateTime EndDate { get; set; } = DateTime.Today;
    public UnavailabilityReasonIds Reason { get; set; } = UnavailabilityReasonIds.Holiday;
    public string ReasonDisplay { get; set; } = "";
    public string Description { get; set; } = "";
    public string DateRangeDisplay { get; set; } = "";
    public int DurationDays { get; set; } = 1;
    #endregion
}
