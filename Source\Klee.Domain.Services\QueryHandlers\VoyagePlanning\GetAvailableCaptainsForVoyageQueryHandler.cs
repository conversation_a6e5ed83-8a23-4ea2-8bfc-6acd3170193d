using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyagePlanning;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Domain.Services.QueryHandlers.VoyagePlanning;

public sealed class GetAvailableCaptainsForVoyageQueryHandler
    : QueryHandlerAsync<GetAvailableCaptainsForVoyageQuery, IReadOnlyList<AvailableCaptainListItem>>
{
    #region PROPERTIES
    private IAppSrpDbContext DbContext { get; }
    #endregion

    #region CONSTRUCTORS
    public GetAvailableCaptainsForVoyageQueryHandler(IAppSrpDbContext dbContext)
    {
        this.DbContext = dbContext;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<AvailableCaptainListItem>> ExecuteAsync(GetAvailableCaptainsForVoyageQuery query,
                                                                        CancellationToken cancellationToken = new CancellationToken())
    {
        // Calculate time buffer for availability check
        TimeSpan buffer = TimeSpan.FromMinutes(15);
        DateTime queryStartWithBuffer = query.VoyageStartDateTime.Subtract(buffer).ToUniversalTime();
        DateTime queryEndWithBuffer = query.VoyageEndDateTime.Add(buffer).ToUniversalTime();

        // Step 1: Get all active operators first and COMPLETE this operation
        List<Operator> allOperators = await DbContext.Set<Operator>()
            .Include(op => op.Organization)
            .Where(op => op.IsActive == true)
            .ToListAsync(cancellationToken);

        // Step 2: Now get conflicting voyages in a completely separate operation
        List<string> unavailableOperatorIds = (await DbContext.Set<Voyage>()
            .Where(v => v.IsActive == true)
            .Where(v => !string.IsNullOrEmpty(v.OperatorId))
            .Where(v => v.EndDateTime > queryStartWithBuffer && v.StartDateTime < queryEndWithBuffer)
            .Select(v => v.OperatorId)
            .Distinct()
            .ToListAsync(cancellationToken))!;

        // Step 2.5: Get operators with unavailable dates that overlap with voyage dates
        DateTime voyageStartDate = query.VoyageStartDateTime.Date;
        DateTime voyageEndDate = query.VoyageEndDateTime.Date;

        // Get operators who have unavailable dates within the voyage period
        List<string> unavailableOperatorIdsFromDates = await DbContext.Set<Operator>()
            .Where(op => op.IsActive == true)
            .Where(op => op.UnavailableDates.Any(ud => ud.Date >= voyageStartDate && ud.Date <= voyageEndDate))
            .Select(op => op.OperatorId)
            .Distinct()
            .ToListAsync(cancellationToken);

        // Combine both unavailability sources
        unavailableOperatorIds = unavailableOperatorIds
            .Union(unavailableOperatorIdsFromDates)
            .ToList();

        // Step 3: Filter operators based on conflicts and requirements
        List<AvailableCaptainListItem> availableCaptains = allOperators
            .Where(op => !unavailableOperatorIds.Contains(op.OperatorId))
            .Where(op => HasRequiredQualifications(op.Qualifications, query.RequiredQualifications))
            .Where(op => IsAvailableForVoyageDates(op, query.VoyageStartDateTime, query.VoyageEndDateTime))
            .Select(op => new AvailableCaptainListItem
            {
                EntityId = op.EntityId,
                OperatorId = op.OperatorId,
                FirstName = op.FirstName,
                LastName = op.LastName,
                Email = op.OperatorEmail,
                Biography = op.Biography,
                OrganizationName = op.Organization.Name,
                OrganizationId = op.OrganizationId,
                YearsOfExperience = op.YearsOfExperience,
                YearsOfRemoteExperience = op.YearsOfRemoteExperience,
                HourlyRateInEuros = op.HourlyRateInEuros,
                Qualifications = op.Qualifications,
                WorkingDays = op.WorkingDays,
                RegularStartTime = op.RegularStartTime.ToLocalTime(),
                RegularEndTime = op.RegularEndTime.ToLocalTime(),
                IsActive = op.IsActive ?? false
            })
            .OrderBy(op => op.HourlyRateInEuros)
            .ThenBy(op => op.LastName)
            .ToList();

        return availableCaptains;
    }

    private bool HasRequiredQualifications(List<QualificationTypeIds> operatorQualifications, List<QualificationTypeIds> requiredQualifications)
    {
        //No requirements needed
        if (!requiredQualifications.Any())
            return true;

        return requiredQualifications.All(required => operatorQualifications.Contains(required));
    }

    private bool IsAvailableForVoyageDates(Operator operatorData, DateTime voyageStart, DateTime voyageEnd)
    {
        // Basic availability check - can be enhanced with more complex scheduling logic
        // For now, check if operator works on the days of the week for the voyage period
        DayOfWeek startDay = voyageStart.DayOfWeek;
        WeekDaysIds operatorWorkingDays = operatorData.WorkingDays;

        bool isAvailable = startDay switch {
            DayOfWeek.Monday => operatorWorkingDays.HasFlag(WeekDaysIds.Monday),
            DayOfWeek.Tuesday => operatorWorkingDays.HasFlag(WeekDaysIds.Tuesday),
            DayOfWeek.Wednesday => operatorWorkingDays.HasFlag(WeekDaysIds.Wednesday),
            DayOfWeek.Thursday => operatorWorkingDays.HasFlag(WeekDaysIds.Thursday),
            DayOfWeek.Friday => operatorWorkingDays.HasFlag(WeekDaysIds.Friday),
            DayOfWeek.Saturday => operatorWorkingDays.HasFlag(WeekDaysIds.Saturday),
            DayOfWeek.Sunday => operatorWorkingDays.HasFlag(WeekDaysIds.Sunday),
            _ => false
        };

        // Check if the operator's working hours overlap with the voyage start and end times
        if (isAvailable)
        {
            DateTime operatorStart = operatorData.RegularStartTime;
            DateTime operatorEnd = operatorData.RegularEndTime;
            // Check if the voyage start and end times fall within the operator's working hours
            isAvailable = voyageStart.TimeOfDay >= operatorStart.TimeOfDay &&
                          voyageEnd.TimeOfDay <= operatorEnd.TimeOfDay;
        }
        return isAvailable;
    }

    #endregion
}
