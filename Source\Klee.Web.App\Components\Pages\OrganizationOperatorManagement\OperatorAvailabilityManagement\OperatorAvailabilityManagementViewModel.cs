using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using EnumsNET;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Commands.OperatorManagement.OperatorUnavailability;
using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability;
using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability.Data;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Messages.Queries.Common;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OperatorAvailabilityManagement;

public class OperatorAvailabilityManagementViewModel : ViewModelBase<OperatorAvailabilityManagementViewModel>
{
    #region PROPERTIES - STATIC
    public static IList<string> SelectableReasonDisplayNames { get; } = Enums.GetMembers<UnavailabilityReasonIds>()
        .Where(_ => _.Value != UnavailabilityReasonIds.None)
        .Select(_ => _.AsString(EnumFormat.DisplayName))
        .OrderBy(_ => _)
        .ToList();
    #endregion

    #region PROPERTIES - DATA
    public string OperatorId { get; set; } = "";
    public string OperatorDisplayName { get; set; } = "";
    public List<OperatorUnavailabilityListItem> UnavailabilityPeriods { get; set; } = new();
    #endregion

    #region PROPERTIES - FORM
    [Required(ErrorMessage = "Start date is required")]
    public DateTime StartDate { get; set; } = DateTime.Today;

    [Required(ErrorMessage = "End date is required")]
    public DateTime EndDate { get; set; } = DateTime.Today;

    [Required(ErrorMessage = "Reason is required")]
    public string ReasonDisplayName { get; set; } = UnavailabilityReasonIds.Holiday.AsString(EnumFormat.DisplayName);

    public string Description { get; set; } = "";
    #endregion

    #region PROPERTIES - UI STATE
    public bool IsLoading { get; set; } = false;
    public bool IsSubmitting { get; set; } = false;
    public bool IsEditMode { get; set; } = false;
    public string EditingUnavailabilityId { get; set; } = "";
    #endregion

    #region PROPERTIES - SERVICES
    private ISrpProcessors SrpProcessors { get; }
    #endregion

    #region CONSTRUCTORS
    public OperatorAvailabilityManagementViewModel(ISrpProcessors srpProcessors)
    {
        this.SrpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA LOADING
    public async Task LoadUnavailabilityPeriodsAsync()
    {
        if (string.IsNullOrEmpty(OperatorId))
            return;

        IsLoading = true;

        try
        {
            var query = new GetOperatorUnavailabilityListQuery(await SrpProcessors.GetQueryContextAsync())
            {
                OperatorId = this.OperatorId
            };

            UnavailabilityPeriods = (await SrpProcessors.QueryProcessor.ExecuteAsync(query)).ToList();
        }
        finally
        {
            IsLoading = false;
        }
    }
    #endregion

    #region METHODS - FORM OPERATIONS
    public async Task CreateUnavailabilityAsync()
    {
        var command = new CreateOperatorUnavailabilityCommand(await SrpProcessors.GetCommandContextAsync())
        {
            OperatorId = this.OperatorId,
            StartDate = this.StartDate,
            EndDate = this.EndDate,
            Reason = GetReasonFromDisplayName(this.ReasonDisplayName),
            Description = this.Description
        };

        await SrpProcessors.CommandProcessor.SendAsync(command);
        await LoadUnavailabilityPeriodsAsync();
        ResetForm();
    }

    public async Task UpdateUnavailabilityAsync()
    {
        var command = new UpdateOperatorUnavailabilityCommand(await SrpProcessors.GetCommandContextAsync())
        {
            UnavailabilityId = this.EditingUnavailabilityId,
            StartDate = this.StartDate,
            EndDate = this.EndDate,
            Reason = GetReasonFromDisplayName(this.ReasonDisplayName),
            Description = this.Description
        };

        await SrpProcessors.CommandProcessor.SendAsync(command);
        await LoadUnavailabilityPeriodsAsync();
        ResetForm();
    }

    public async Task DeleteUnavailabilityAsync(string unavailabilityId)
    {
        var command = new DeleteOperatorUnavailabilityCommand(await SrpProcessors.GetCommandContextAsync())
        {
            UnavailabilityId = unavailabilityId
        };

        await SrpProcessors.CommandProcessor.SendAsync(command);
        await LoadUnavailabilityPeriodsAsync();
    }

    public void StartEdit(OperatorUnavailabilityListItem item)
    {
        IsEditMode = true;
        EditingUnavailabilityId = item.UnavailabilityId;
        StartDate = item.StartDate;
        EndDate = item.EndDate;
        ReasonDisplayName = item.ReasonDisplay;
        Description = item.Description;
    }

    public void ResetForm()
    {
        IsEditMode = false;
        EditingUnavailabilityId = "";
        StartDate = DateTime.Today;
        EndDate = DateTime.Today;
        ReasonDisplayName = UnavailabilityReasonIds.Holiday.AsString(EnumFormat.DisplayName);
        Description = "";
    }
    #endregion

    #region METHODS - VALIDATION
    public async Task<IEnumerable<ValidationResult>> ValidateAsync()
    {
        var results = new List<ValidationResult>();

        // Validate date range
        if (StartDate > EndDate)
        {
            results.Add(new ValidationResult("Start date cannot be after end date.", new[] { nameof(StartDate) }));
        }

        // Validate past dates
        if (StartDate < DateTime.Today)
        {
            results.Add(new ValidationResult("Cannot create unavailability periods in the past.", new[] { nameof(StartDate) }));
        }

        return results;
    }
    #endregion

    #region METHODS - HELPERS
    private UnavailabilityReasonIds GetReasonFromDisplayName(string displayName)
    {
        return Enums.GetMembers<UnavailabilityReasonIds>()
            .FirstOrDefault(m => m.AsString(EnumFormat.DisplayName) == displayName)?.Value ?? UnavailabilityReasonIds.Holiday;
    }
    #endregion
}
