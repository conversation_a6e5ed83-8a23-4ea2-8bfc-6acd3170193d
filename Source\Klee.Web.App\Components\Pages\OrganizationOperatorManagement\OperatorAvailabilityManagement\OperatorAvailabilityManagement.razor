@using AntDesign
@using Klee.Web.App.Components.UI
@using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability.Data
@using EnumsNET

@inherits OperatorAvailabilityManagementViewBase

<Modal Title=@($"Manage Availability - {OperatorDisplayName}")
       @bind-Visible="Visible"
       OnCancel="HandleModalCancel"
       Width="800"
       Footer="null">
    
    @if (ViewModel?.IsLoading == true)
    {
        <div class="text-center py-12">
            <Spin Size="SpinSize.Large" />
            <p class="mt-4 text-gray-600">Loading availability data...</p>
        </div>
    }
    else
    {
        <div class="space-y-6">
            <!-- Add/Edit Form -->
            <Card Class="@TailwindStyleStrings.Card.Container">
                <div class="p-4">
                    <h3 class="text-lg font-medium text-teal-700 mb-4">
                        @(ViewModel?.IsEditMode == true ? "Edit Unavailability Period" : "Add Unavailability Period")
                    </h3>
                    
                    @if (ValidationErrors.Any())
                    {
                        <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            @foreach (var error in ValidationErrors)
                            {
                                <p class="text-sm text-red-600">@error</p>
                            }
                        </div>
                    }

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-teal-700 mb-1">Start Date</label>
                            <DatePicker @bind-Value="@ViewModel.StartDate"
                                        Format="dd/MM/yyyy"
                                        Placeholder=@("Select start date")
                                        Class="@TailwindStyleStrings.Form.Input" />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-teal-700 mb-1">End Date</label>
                            <DatePicker @bind-Value="@ViewModel.EndDate"
                                        Format="dd/MM/yyyy"
                                        Placeholder=@("Select end date")
                                        Class="@TailwindStyleStrings.Form.Input" />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-teal-700 mb-1">Reason</label>
                            <Select @bind-Value="@ViewModel.ReasonDisplayName"
                                    Placeholder="Select reason"
                                    Class="@TailwindStyleStrings.Form.Select">
                                @foreach (var reason in OperatorAvailabilityManagementViewModel.SelectableReasonDisplayNames)
                                {
                                    <SelectOption Value="@reason">@reason</SelectOption>
                                }
                            </Select>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-teal-700 mb-1">Description (Optional)</label>
                            <TextArea @bind-Value="@ViewModel.Description"
                                      Placeholder="Additional notes..."
                                      Rows="2"
                                      Class="@TailwindStyleStrings.Form.TextArea" />
                        </div>
                    </div>

                    <div class="flex justify-end gap-2 mt-4">
                        @if (ViewModel?.IsEditMode == true)
                        {
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="HandleCancelEdit">
                                Cancel
                            </Button>
                        }
                        
                        <Button Type="@ButtonType.Primary"
                                Class="@TailwindStyleStrings.Button.Primary"
                                Loading="@(ViewModel?.IsSubmitting == true)"
                                OnClick="HandleSubmit">
                            @(ViewModel?.IsEditMode == true ? "Update" : "Add") Period
                        </Button>
                    </div>
                </div>
            </Card>

            <!-- Existing Periods List -->
            <Card Class="@TailwindStyleStrings.Card.Container">
                <div class="p-4">
                    <h3 class="text-lg font-medium text-teal-700 mb-4">Current Unavailability Periods</h3>
                    
                    @if (ViewModel?.UnavailabilityPeriods?.Any() == true)
                    {
                        <div class="space-y-3">
                            @foreach (var period in ViewModel.UnavailabilityPeriods)
                            {
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md border">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                                @period.ReasonDisplay
                                            </span>
                                            <span class="font-medium text-gray-900">@period.DateRangeDisplay</span>
                                            <span class="text-sm text-gray-500">(@period.DurationDays day@(period.DurationDays != 1 ? "s" : ""))</span>
                                        </div>
                                        @if (!string.IsNullOrEmpty(period.Description))
                                        {
                                            <p class="text-sm text-gray-600 mt-1">@period.Description</p>
                                        }
                                    </div>
                                    
                                    <div class="flex items-center gap-2">
                                        <Button Type="@ButtonType.Link"
                                                Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                                OnClick="@(() => HandleEdit(period))">
                                            <i class="@($"fas fa-pencil-alt h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        </Button>
                                        
                                        <Button Type="@ButtonType.Link"
                                                Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                                OnClick="@(() => HandleDeleteClick(period))">
                                            <i class="fas fa-trash h-4 w-4 text-red-600 hover:text-red-700"></i>
                                        </Button>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-8">
                            <i class="fas fa-calendar-check text-4xl text-gray-300 mb-3"></i>
                            <p class="text-gray-500">No unavailability periods defined</p>
                            <p class="text-sm text-gray-400">This operator is available for all dates</p>
                        </div>
                    }
                </div>
            </Card>
        </div>
    }
</Modal>

<!-- Delete Confirmation Modal -->
<Modal Title="Confirm Delete"
       @bind-Visible="ShowDeleteConfirmModal"
       OnOk="HandleConfirmDelete"
       OnCancel="HandleCancelDelete"
       OkText=@("Delete")
       CancelText=@("Cancel")
       OkButtonProps="@(new ButtonProps { Danger = true })">
    <div>
        <p>Are you sure you want to delete this unavailability period?</p>
        <p class="font-medium text-gray-900 mt-2">@DeletingDescription</p>
        <p class="text-sm text-gray-600 mt-2">This action cannot be undone.</p>
    </div>
</Modal>
