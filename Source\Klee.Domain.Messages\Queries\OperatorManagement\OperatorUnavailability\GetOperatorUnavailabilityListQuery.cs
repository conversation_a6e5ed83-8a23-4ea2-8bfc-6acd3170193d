using System.Collections.Generic;
using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability;

public class GetOperatorUnavailabilityListQuery
    : QueryBase<IReadOnlyList<OperatorUnavailabilityListItem>>
{
    #region PROPERTIES
    public string OperatorId { get; set; } = "";
    #endregion

    #region CONSTRUCTORS
    public GetOperatorUnavailabilityListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}
