using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OperatorManagement.OperatorUnavailability;

public class DeleteOperatorUnavailabilityCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public bool Success { get; set; } = false;
    }
    #endregion

    #region PROPERTIES
    public string UnavailabilityId { get; set; } = "";

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public DeleteOperatorUnavailabilityCommand(ICommandContext context)
        : base(context)
    {
    }
    #endregion
}
