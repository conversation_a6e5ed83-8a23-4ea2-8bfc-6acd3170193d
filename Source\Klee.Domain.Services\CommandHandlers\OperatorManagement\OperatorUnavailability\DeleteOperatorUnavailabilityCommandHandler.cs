using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Commands.OperatorManagement.OperatorUnavailability;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.OperatorUnavailability;

public sealed class DeleteOperatorUnavailabilityCommandHandler
    : RequestHandlerAsync<DeleteOperatorUnavailabilityCommand>
{
    #region PROPERTIES
    private IAppSrpDbContext DbContext { get; }
    private ILogger<DeleteOperatorUnavailabilityCommandHandler> Logger { get; }
    #endregion

    #region CONSTRUCTORS
    public DeleteOperatorUnavailabilityCommandHandler(
        IAppSrpDbContext dbContext,
        ILogger<DeleteOperatorUnavailabilityCommandHandler> logger)
    {
        this.DbContext = dbContext;
        this.Logger = logger;
    }
    #endregion

    #region METHODS
    public override async Task<DeleteOperatorUnavailabilityCommand> HandleAsync(DeleteOperatorUnavailabilityCommand command,
                                                                                CancellationToken cancellationToken = new CancellationToken())
    {
        // Find the unavailability period
        var unavailability = await DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability>()
            .FirstOrDefaultAsync(u => u.UnavailabilityId == command.UnavailabilityId && u.IsActive == true, cancellationToken);

        if (unavailability == null)
        {
            throw new ArgumentException($"Unavailability period with ID {command.UnavailabilityId} not found.");
        }

        // Soft delete by setting IsActive to false
        var isActiveProperty = typeof(Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability)
            .GetProperty(nameof(unavailability.IsActive));
        isActiveProperty?.SetValue(unavailability, false);

        await DbContext.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Deleted unavailability period {UnavailabilityId} for operator {OperatorId}",
            command.UnavailabilityId, unavailability.OperatorId);

        command.Result.Success = true;
        return command;
    }
    #endregion
}
