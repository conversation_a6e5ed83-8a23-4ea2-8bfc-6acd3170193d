using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using EnumsNET;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators.Data;
using Klee.Domain.Services;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OperatorCalendarAvailability;

public class OperatorCalendarAvailabilityViewModel
{
    #region PROPERTIES - STATIC
    public static IList<string> SelectableReasonDisplayNames { get; } = Enums.GetMembers<UnavailabilityReasonIds>()
        .Where(_ => _.Value != UnavailabilityReasonIds.None)
        .Select(_ => _.AsString(EnumFormat.DisplayName))
        .OrderBy(_ => _)
        .ToList();
    #endregion

    #region PROPERTIES - DATA
    public string OperatorId { get; set; } = "";
    public string OperatorDisplayName { get; set; } = "";
    public OrganizationOperatorListItem? OperatorData { get; set; }
    public HashSet<DateTime> UnavailableDates { get; set; } = new();
    public Dictionary<DateTime, OperatorUnavailableDate> UnavailableDateDetails { get; set; } = new();
    #endregion

    #region PROPERTIES - CALENDAR STATE
    public HashSet<DateTime> SelectedDates { get; set; } = new();
    public DateTime CurrentMonth { get; set; } = DateTime.Today;
    public bool IsSelectingRange { get; set; } = false;
    public DateTime? RangeStartDate { get; set; }
    #endregion

    #region PROPERTIES - FORM
    [Required(ErrorMessage = "Reason is required")]
    public string ReasonDisplayName { get; set; } = UnavailabilityReasonIds.Holiday.AsString(EnumFormat.DisplayName);

    public string Description { get; set; } = "";
    #endregion

    #region PROPERTIES - UI STATE
    public bool IsLoading { get; set; } = false;
    public bool IsSubmitting { get; set; } = false;
    public bool ShowCalendarModal { get; set; } = false;
    #endregion

    #region PROPERTIES - SERVICES
    private ISrpProcessors SrpProcessors { get; }
    #endregion

    #region CONSTRUCTORS
    public OperatorCalendarAvailabilityViewModel(ISrpProcessors srpProcessors)
    {
        this.SrpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA LOADING
    public async Task LoadOperatorDataAsync()
    {
        if (string.IsNullOrEmpty(OperatorId))
            return;

        IsLoading = true;

        try
        {
            var query = new GetOrganizationOperatorListQuery(await SrpProcessors.GetQueryContextAsync());
            var operators = await SrpProcessors.QueryProcessor.ExecuteAsync(query);
            
            OperatorData = operators.FirstOrDefault(o => o.OperatorId == OperatorId);
            
            if (OperatorData != null)
            {
                OperatorDisplayName = OperatorData.OperatorDisplayName;
                LoadUnavailableDatesFromOperator();
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void LoadUnavailableDatesFromOperator()
    {
        UnavailableDates.Clear();
        UnavailableDateDetails.Clear();

        // TODO: Load actual unavailable dates from the operator entity
        // This will require extending the query to include unavailable dates
        // For now, this is a placeholder - the actual implementation would load
        // the unavailable dates from the operator's UnavailableDates collection
    }
    #endregion

    #region METHODS - CALENDAR OPERATIONS
    public void ToggleDateSelection(DateTime date)
    {
        if (date < DateTime.Today)
            return; // Don't allow selection of past dates

        if (IsSelectingRange)
        {
            HandleRangeSelection(date);
        }
        else
        {
            if (SelectedDates.Contains(date))
            {
                SelectedDates.Remove(date);
            }
            else
            {
                SelectedDates.Add(date);
            }
        }
    }

    private void HandleRangeSelection(DateTime date)
    {
        if (RangeStartDate == null)
        {
            RangeStartDate = date;
            SelectedDates.Clear();
            SelectedDates.Add(date);
        }
        else
        {
            var startDate = RangeStartDate.Value;
            var endDate = date;
            
            if (endDate < startDate)
            {
                (startDate, endDate) = (endDate, startDate);
            }

            SelectedDates.Clear();
            for (var d = startDate; d <= endDate; d = d.AddDays(1))
            {
                if (d >= DateTime.Today) // Only add future dates
                {
                    SelectedDates.Add(d);
                }
            }

            RangeStartDate = null;
        }
    }

    public void ToggleRangeSelectionMode()
    {
        IsSelectingRange = !IsSelectingRange;
        RangeStartDate = null;
        if (!IsSelectingRange)
        {
            SelectedDates.Clear();
        }
    }

    public void ClearSelection()
    {
        SelectedDates.Clear();
        RangeStartDate = null;
    }

    public void SelectAllUnavailableDates()
    {
        SelectedDates.Clear();
        foreach (var date in UnavailableDates.Where(d => d >= DateTime.Today))
        {
            SelectedDates.Add(date);
        }
    }
    #endregion

    #region METHODS - FORM OPERATIONS
    public async Task AddSelectedDatesAsync()
    {
        if (!SelectedDates.Any())
            return;

        var command = new UpdateOperatorUnavailableDatesCommand(await SrpProcessors.GetCommandContextAsync())
        {
            OperatorId = this.OperatorId,
            DatesToAdd = SelectedDates.ToList(),
            DatesToRemove = new List<DateTime>(),
            Reason = GetReasonFromDisplayName(this.ReasonDisplayName),
            Description = this.Description
        };

        await SrpProcessors.CommandProcessor.SendAsync(command);
        
        // Update local state
        foreach (var date in SelectedDates)
        {
            UnavailableDates.Add(date);
            UnavailableDateDetails[date] = new OperatorUnavailableDate(date, command.Reason, command.Description);
        }

        ClearSelection();
        ResetForm();
    }

    public async Task RemoveSelectedDatesAsync()
    {
        if (!SelectedDates.Any())
            return;

        var command = new UpdateOperatorUnavailableDatesCommand(await SrpProcessors.GetCommandContextAsync())
        {
            OperatorId = this.OperatorId,
            DatesToAdd = new List<DateTime>(),
            DatesToRemove = SelectedDates.ToList(),
            Reason = UnavailabilityReasonIds.Holiday, // Not used for removal
            Description = ""
        };

        await SrpProcessors.CommandProcessor.SendAsync(command);
        
        // Update local state
        foreach (var date in SelectedDates)
        {
            UnavailableDates.Remove(date);
            UnavailableDateDetails.Remove(date);
        }

        ClearSelection();
    }

    public void ResetForm()
    {
        ReasonDisplayName = UnavailabilityReasonIds.Holiday.AsString(EnumFormat.DisplayName);
        Description = "";
    }
    #endregion

    #region METHODS - HELPERS
    private UnavailabilityReasonIds GetReasonFromDisplayName(string displayName)
    {
        return Enums.GetMembers<UnavailabilityReasonIds>()
            .FirstOrDefault(m => m.AsString(EnumFormat.DisplayName) == displayName)?.Value ?? UnavailabilityReasonIds.Holiday;
    }

    public string GetDateStatusClass(DateTime date)
    {
        if (date < DateTime.Today)
            return "past-date";
        
        if (SelectedDates.Contains(date))
            return "selected-date";
            
        if (UnavailableDates.Contains(date))
            return "unavailable-date";
            
        return "available-date";
    }

    public bool IsDateSelectable(DateTime date)
    {
        return date >= DateTime.Today;
    }
    #endregion
}
