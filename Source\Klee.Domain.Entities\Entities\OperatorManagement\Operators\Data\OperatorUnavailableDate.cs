using System;
using System.ComponentModel.DataAnnotations;

namespace Klee.Domain.Entities.OperatorManagement.Operators.Data;

public class OperatorUnavailableDate
{
    #region PROPERTIES
    /// <summary>
    /// The date when the operator is unavailable
    /// </summary>
    [Required]
    public DateTime Date { get; set; } = DateTime.Today;

    /// <summary>
    /// Reason for unavailability on this date
    /// </summary>
    [Required]
    public UnavailabilityReasonIds Reason { get; set; } = UnavailabilityReasonIds.Holiday;

    /// <summary>
    /// Optional description/notes for this unavailable date
    /// </summary>
    public string Description { get; set; } = "";
    #endregion

    #region CONSTRUCTORS
    public OperatorUnavailableDate()
    {
    }

    public OperatorUnavailableDate(DateTime date, UnavailabilityReasonIds reason, string description = "")
    {
        Date = date.Date; // Ensure we only store the date part
        Reason = reason;
        Description = description;
    }
    #endregion

    #region METHODS
    /// <summary>
    /// Checks if this unavailable date matches the given date
    /// </summary>
    public bool IsDateUnavailable(DateTime checkDate)
    {
        return Date.Date == checkDate.Date;
    }

    /// <summary>
    /// Returns a display string for this unavailable date
    /// </summary>
    public string GetDisplayString()
    {
        var reasonDisplay = Reason.ToString(); // You can enhance this with proper display names
        return string.IsNullOrEmpty(Description) 
            ? $"{Date:MMM dd, yyyy} - {reasonDisplay}"
            : $"{Date:MMM dd, yyyy} - {reasonDisplay}: {Description}";
    }
    #endregion

    #region EQUALITY
    public override bool Equals(object obj)
    {
        if (obj is OperatorUnavailableDate other)
        {
            return Date.Date == other.Date.Date;
        }
        return false;
    }

    public override int GetHashCode()
    {
        return Date.Date.GetHashCode();
    }
    #endregion
}
