using System;
using System.Collections.Generic;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators;

public class UpdateOperatorUnavailableDatesCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public bool Success { get; set; } = false;
        public int DatesAdded { get; set; } = 0;
        public int DatesRemoved { get; set; } = 0;
    }
    #endregion

    #region PROPERTIES
    public string OperatorId { get; set; } = "";
    public List<DateTime> DatesToAdd { get; set; } = new();
    public List<DateTime> DatesToRemove { get; set; } = new();
    public UnavailabilityReasonIds Reason { get; set; } = UnavailabilityReasonIds.Holiday;
    public string Description { get; set; } = "";
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateOperatorUnavailableDatesCommand(ICommandContext context)
        : base(context)
    {
    }
    #endregion
}
