using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using EnumsNET;
using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability;
using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability.Data;
using Microsoft.EntityFrameworkCore;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Services.QueryHandlers.OperatorManagement.OperatorUnavailability;

public sealed class GetOperatorUnavailabilityListQueryHandler
    : QueryHandlerAsync<GetOperatorUnavailabilityListQuery, IReadOnlyList<OperatorUnavailabilityListItem>>
{
    #region PROPERTIES
    private IAppSrpDbContext DbContext { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOperatorUnavailabilityListQueryHandler(IAppSrpDbContext dbContext)
    {
        this.DbContext = dbContext;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<OperatorUnavailabilityListItem>> ExecuteAsync(
        GetOperatorUnavailabilityListQuery query,
        CancellationToken cancellationToken = new CancellationToken())
    {
        var unavailabilities = await DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability>()
            .Where(u => u.OperatorId == query.OperatorId && u.IsActive == true)
            .OrderBy(u => u.StartDate)
            .Select(u => new OperatorUnavailabilityListItem
            {
                EntityId = u.EntityId,
                UnavailabilityId = u.UnavailabilityId,
                OperatorId = u.OperatorId,
                StartDate = u.StartDate,
                EndDate = u.EndDate,
                Reason = u.Reason,
                ReasonDisplay = u.Reason.AsString(EnumFormat.DisplayName),
                Description = u.Description,
                DateRangeDisplay = u.StartDate == u.EndDate 
                    ? u.StartDate.ToString("MMM dd, yyyy")
                    : $"{u.StartDate:MMM dd, yyyy} - {u.EndDate:MMM dd, yyyy}",
                DurationDays = (int)(u.EndDate - u.StartDate).TotalDays + 1
            })
            .ToListAsync(cancellationToken);

        return unavailabilities;
    }
    #endregion
}
