using System.ComponentModel.DataAnnotations;

namespace Klee.Domain.Entities.OperatorManagement.Operators.Data;

public enum UnavailabilityReasonIds
{
    [Display(Name = "None")]
    None = 0,
    [Display(Name = "Holiday")]
    Holiday = 1,
    [Display(Name = "Sick Leave")]
    SickLeave = 2,
    [Display(Name = "Personal")]
    Personal = 3,
    [Display(Name = "Training")]
    Training = 4,
    [Display(Name = "Other")]
    Other = 5
}
