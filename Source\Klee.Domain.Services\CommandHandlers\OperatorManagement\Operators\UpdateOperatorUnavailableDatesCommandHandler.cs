using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators;

public sealed class UpdateOperatorUnavailableDatesCommandHandler
    : RequestHandlerAsync<UpdateOperatorUnavailableDatesCommand>
{
    #region PROPERTIES
    private IAppSrpDbContext DbContext { get; }
    private ILogger<UpdateOperatorUnavailableDatesCommandHandler> Logger { get; }
    #endregion

    #region CONSTRUCTORS
    public UpdateOperatorUnavailableDatesCommandHandler(
        IAppSrpDbContext dbContext,
        ILogger<UpdateOperatorUnavailableDatesCommandHandler> logger)
    {
        this.DbContext = dbContext;
        this.Logger = logger;
    }
    #endregion

    #region METHODS
    public override async Task<UpdateOperatorUnavailableDatesCommand> HandleAsync(
        UpdateOperatorUnavailableDatesCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Find the operator
        var operatorEntity = await DbContext.Set<Operator>()
            .FirstOrDefaultAsync(o => o.OperatorId == command.OperatorId && o.IsActive == true, cancellationToken);

        if (operatorEntity == null)
        {
            throw new ArgumentException($"Operator with ID {command.OperatorId} not found or inactive.");
        }

        int datesAdded = 0;
        int datesRemoved = 0;

        // Remove dates
        foreach (var dateToRemove in command.DatesToRemove)
        {
            if (operatorEntity.IsUnavailableOnDate(dateToRemove))
            {
                operatorEntity.RemoveUnavailableDate(dateToRemove);
                datesRemoved++;
            }
        }

        // Add dates
        foreach (var dateToAdd in command.DatesToAdd)
        {
            // Validate date is not in the past
            if (dateToAdd.Date < DateTime.Today)
            {
                Logger.LogWarning("Skipping past date {Date} for operator {OperatorId}", dateToAdd, command.OperatorId);
                continue;
            }

            operatorEntity.AddUnavailableDate(dateToAdd, command.Reason, command.Description);
            datesAdded++;
        }

        await DbContext.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Updated unavailable dates for operator {OperatorId}: {DatesAdded} added, {DatesRemoved} removed",
            command.OperatorId, datesAdded, datesRemoved);

        command.Result.Success = true;
        command.Result.DatesAdded = datesAdded;
        command.Result.DatesRemoved = datesRemoved;

        return command;
    }
    #endregion
}
