using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Messages.Queries.OperatorManagement.OperatorUnavailability.Data;
using Klee.Web.App.Services.UserAuthentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OperatorAvailabilityManagement;

public class OperatorAvailabilityManagementViewBase : ComponentBase
{
    #region DI
    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<OperatorAvailabilityManagementViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter]
    public string OperatorId { get; set; } = "";

    [Parameter]
    public string OperatorDisplayName { get; set; } = "";

    [Parameter]
    public bool Visible { get; set; } = false;

    [Parameter]
    public EventCallback<bool> VisibleChanged { get; set; }

    [Parameter]
    public EventCallback OnClose { get; set; }
    #endregion

    #region PROPERTIES
    protected OperatorAvailabilityManagementViewModel ViewModel { get; set; }
    protected List<string> ValidationErrors { get; set; } = new();
    protected bool ShowDeleteConfirmModal { get; set; } = false;
    protected string DeletingUnavailabilityId { get; set; } = "";
    protected string DeletingDescription { get; set; } = "";
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Check user authorization
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

        if (!isUserOrganizationAdmin)
        {
            await CloseModal();
            return;
        }

        // Initialize ViewModel
        this.ViewModel = new OperatorAvailabilityManagementViewModel(this.SrpProcessors);

        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ViewModel != null && !string.IsNullOrEmpty(OperatorId))
        {
            ViewModel.OperatorId = OperatorId;
            ViewModel.OperatorDisplayName = OperatorDisplayName;

            if (Visible)
            {
                await ViewModel.LoadUnavailabilityPeriodsAsync();
                StateHasChanged();
            }
        }

        await base.OnParametersSetAsync();
    }
    #endregion

    #region EVENT HANDLERS
    protected async Task HandleModalCancel()
    {
        await CloseModal();
    }

    protected async Task HandleSubmit()
    {
        if (ViewModel.IsSubmitting)
            return;

        try
        {
            ViewModel.IsSubmitting = true;
            ValidationErrors.Clear();
            StateHasChanged();

            List<ValidationResult> validationResult = (await ViewModel.ValidateAsync()).ToList();

            if (validationResult.Count == 0)
            {
                if (ViewModel.IsEditMode)
                {
                    await ViewModel.UpdateUnavailabilityAsync();
                    
                    NotificationService.Success(new NotificationConfig()
                    {
                        Message = "Success",
                        Description = "Unavailability period has been updated successfully",
                        Duration = 4.0
                    });
                }
                else
                {
                    await ViewModel.CreateUnavailabilityAsync();
                    
                    NotificationService.Success(new NotificationConfig()
                    {
                        Message = "Success",
                        Description = "Unavailability period has been created successfully",
                        Duration = 4.0
                    });
                }
            }
            else
            {
                foreach (ValidationResult result in validationResult)
                {
                    if (result.ErrorMessage != null)
                    {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error while saving unavailability period");
            ValidationErrors.Add(ex.Message);
        }
        finally
        {
            ViewModel.IsSubmitting = false;
            StateHasChanged();
        }
    }

    protected void HandleEdit(OperatorUnavailabilityListItem item)
    {
        ViewModel.StartEdit(item);
        StateHasChanged();
    }

    protected void HandleCancelEdit()
    {
        ViewModel.ResetForm();
        StateHasChanged();
    }

    protected void HandleDeleteClick(OperatorUnavailabilityListItem item)
    {
        DeletingUnavailabilityId = item.UnavailabilityId;
        DeletingDescription = $"{item.ReasonDisplay}: {item.DateRangeDisplay}";
        ShowDeleteConfirmModal = true;
        StateHasChanged();
    }

    protected async Task HandleConfirmDelete()
    {
        try
        {
            await ViewModel.DeleteUnavailabilityAsync(DeletingUnavailabilityId);
            
            NotificationService.Success(new NotificationConfig()
            {
                Message = "Success",
                Description = "Unavailability period has been deleted successfully",
                Duration = 4.0
            });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error while deleting unavailability period");
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = ex.Message,
                Duration = 4.0
            });
        }
        finally
        {
            ShowDeleteConfirmModal = false;
            DeletingUnavailabilityId = "";
            DeletingDescription = "";
            StateHasChanged();
        }
    }

    protected void HandleCancelDelete()
    {
        ShowDeleteConfirmModal = false;
        DeletingUnavailabilityId = "";
        DeletingDescription = "";
        StateHasChanged();
    }

    private async Task CloseModal()
    {
        if (ViewModel != null)
        {
            ViewModel.ResetForm();
        }
        
        await VisibleChanged.InvokeAsync(false);
        await OnClose.InvokeAsync();
    }
    #endregion
}
