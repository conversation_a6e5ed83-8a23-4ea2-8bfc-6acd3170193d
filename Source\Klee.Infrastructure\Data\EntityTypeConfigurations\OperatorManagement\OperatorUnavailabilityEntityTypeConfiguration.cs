using Klee.Domain.Entities.OperatorManagement.Operators;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.OperatorManagement;

public class OperatorUnavailabilityEntityTypeConfiguration : IEntityTypeConfiguration<OperatorUnavailability>
{
    public void Configure(EntityTypeBuilder<OperatorUnavailability> builder)
    {
        // Primary key and unique constraints
        builder.HasIndex(_ => _.UnavailabilityId)
            .IsUnique();
        builder.Property(_ => _.UnavailabilityId)
            .IsRequired();
        
        // Required properties
        builder.Property(_ => _.OperatorId)
            .IsRequired();
        builder.Property(_ => _.StartDate)
            .IsRequired();
        builder.Property(_ => _.EndDate)
            .IsRequired();
        builder.Property(_ => _.Reason)
            .IsRequired();

        // Create index to improve performance of availability queries
        builder.HasIndex(u => new { u.OperatorId, u.StartDate, u.EndDate, u.IsActive })
               .HasDatabaseName("IX_OperatorUnavailability_OperatorAvailability");

        // Soft delete filter
        builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

        // Set up the relationship with operator
        builder
            .HasOne(u => u.Operator)
            .WithMany()
            .HasForeignKey(u => u.OperatorId)
            .HasPrincipalKey(o => o.OperatorId)
            .IsRequired();
    }
}
