using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OperatorCalendarAvailability;

public class OperatorCalendarAvailabilityViewBase : ComponentBase
{
    #region DI
    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<OperatorCalendarAvailabilityViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter]
    public string OperatorId { get; set; } = "";

    [Parameter]
    public string OperatorDisplayName { get; set; } = "";

    [Parameter]
    public bool Visible { get; set; } = false;

    [Parameter]
    public EventCallback<bool> VisibleChanged { get; set; }

    [Parameter]
    public EventCallback OnClose { get; set; }
    #endregion

    #region PROPERTIES
    protected OperatorCalendarAvailabilityViewModel ViewModel { get; set; }
    protected List<string> ValidationErrors { get; set; } = new();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Check user authorization
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

        if (!isUserOrganizationAdmin)
        {
            await CloseModal();
            return;
        }

        // Initialize ViewModel
        this.ViewModel = new OperatorCalendarAvailabilityViewModel(this.SrpProcessors);

        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ViewModel != null && !string.IsNullOrEmpty(OperatorId))
        {
            ViewModel.OperatorId = OperatorId;
            ViewModel.OperatorDisplayName = OperatorDisplayName;

            if (Visible)
            {
                await ViewModel.LoadOperatorDataAsync();
                StateHasChanged();
            }
        }

        await base.OnParametersSetAsync();
    }
    #endregion

    #region EVENT HANDLERS
    protected async Task HandleModalCancel()
    {
        await CloseModal();
    }

    protected void HandleDateClick(DateTime date)
    {
        ViewModel.ToggleDateSelection(date);
        StateHasChanged();
    }

    protected void HandleClearSelection()
    {
        ViewModel.ClearSelection();
        StateHasChanged();
    }

    protected async Task HandleAddDates()
    {
        if (!ViewModel.SelectedDates.Any())
        {
            NotificationService.Warning(new NotificationConfig()
            {
                Message = "No Dates Selected",
                Description = "Please select dates to mark as unavailable",
                Duration = 3.0
            });
            return;
        }

        try
        {
            ViewModel.IsSubmitting = true;
            ValidationErrors.Clear();
            StateHasChanged();

            await ViewModel.AddSelectedDatesAsync();

            NotificationService.Success(new NotificationConfig()
            {
                Message = "Success",
                Description = $"{ViewModel.SelectedDates.Count} date(s) marked as unavailable",
                Duration = 4.0
            });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error while adding unavailable dates");
            ValidationErrors.Add(ex.Message);
        }
        finally
        {
            ViewModel.IsSubmitting = false;
            StateHasChanged();
        }
    }

    protected async Task HandleRemoveDates()
    {
        if (!ViewModel.SelectedDates.Any())
        {
            NotificationService.Warning(new NotificationConfig()
            {
                Message = "No Dates Selected",
                Description = "Please select unavailable dates to remove",
                Duration = 3.0
            });
            return;
        }

        try
        {
            ViewModel.IsSubmitting = true;
            StateHasChanged();

            await ViewModel.RemoveSelectedDatesAsync();

            NotificationService.Success(new NotificationConfig()
            {
                Message = "Success",
                Description = $"{ViewModel.SelectedDates.Count} date(s) marked as available",
                Duration = 4.0
            });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error while removing unavailable dates");
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = ex.Message,
                Duration = 4.0
            });
        }
        finally
        {
            ViewModel.IsSubmitting = false;
            StateHasChanged();
        }
    }



    private async Task CloseModal()
    {
        if (ViewModel != null)
        {
            ViewModel.ClearSelection();
            ViewModel.ResetForm();
        }
        
        await VisibleChanged.InvokeAsync(false);
        await OnClose.InvokeAsync();
    }
    #endregion

    #region HELPER METHODS
    // Helper methods for Ant Design Calendar integration
    #endregion
}
