using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Commands.OperatorManagement.OperatorUnavailability;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.OperatorUnavailability;

public sealed class CreateOperatorUnavailabilityCommandHandler
    : RequestHandlerAsync<CreateOperatorUnavailabilityCommand>
{
    #region PROPERTIES
    private IAppSrpDbContext DbContext { get; }
    private ILogger<CreateOperatorUnavailabilityCommandHandler> Logger { get; }
    #endregion

    #region CONSTRUCTORS
    public CreateOperatorUnavailabilityCommandHandler(
        IAppSrpDbContext dbContext,
        ILogger<CreateOperatorUnavailabilityCommandHandler> logger)
    {
        this.DbContext = dbContext;
        this.Logger = logger;
    }
    #endregion

    #region METHODS
    public override async Task<CreateOperatorUnavailabilityCommand> HandleAsync(CreateOperatorUnavailabilityCommand command,
                                                                                CancellationToken cancellationToken = new CancellationToken())
    {
        // Validate dates
        if (command.StartDate > command.EndDate)
        {
            throw new ArgumentException("Start date cannot be after end date.");
        }

        // Validate operator exists
        bool operatorExists = await DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.Operator>()
            .AnyAsync(o => o.OperatorId == command.OperatorId && o.IsActive == true, cancellationToken);

        if (!operatorExists)
        {
            throw new ArgumentException($"Operator with ID {command.OperatorId} not found or inactive.");
        }

        // Check for overlapping unavailability periods
        bool hasOverlap = await DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability>()
            .AnyAsync(u => u.OperatorId == command.OperatorId &&
                          u.IsActive == true &&
                          u.StartDate <= command.EndDate &&
                          u.EndDate >= command.StartDate, cancellationToken);

        if (hasOverlap)
        {
            throw new InvalidOperationException("The specified period overlaps with an existing unavailability period.");
        }

        // Create unavailability period
        var unavailability = new Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability(
            command.OperatorId,
            command.StartDate,
            command.EndDate,
            command.Reason,
            command.Description);

        DbContext.Set<Klee.Domain.Entities.OperatorManagement.Operators.OperatorUnavailability>().Add(unavailability);
        await DbContext.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Created unavailability period {UnavailabilityId} for operator {OperatorId} from {StartDate} to {EndDate}",
            unavailability.UnavailabilityId, command.OperatorId, command.StartDate, command.EndDate);

        command.Result.UnavailabilityId = unavailability.UnavailabilityId;
        return command;
    }
    #endregion
}
